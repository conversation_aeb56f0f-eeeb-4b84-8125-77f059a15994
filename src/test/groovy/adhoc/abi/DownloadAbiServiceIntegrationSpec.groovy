package adhoc.abi

import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.websocket.events.NewHeadsNotification
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.AttributeValue
import software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest
import software.amazon.awssdk.services.dynamodb.model.ScanRequest
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import spock.lang.Shared
import spock.lang.Specification

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class DownloadAbiServiceIntegrationSpec extends Specification {

    @Shared
    DynamoDbClient dynamoDbClient

    @Shared
    S3Client s3Client

    @Shared
    def logger = LoggerFactory.getLogger(LoggingService.class) as Logger

    @Shared
    def logger2 = LoggerFactory.getLogger(AbiParser.class) as Logger

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

    @Autowired
    BcmonitoringConfigurationProperties properties

    static final String TEST_BUCKET = "abijson-local-bucket"
    static final String EVENTS_TABLE = "local-Events"        // Same as default in application.properties
    static final String BLOCK_HEIGHT_TABLE = "local-BlockHeight"  // Same as default in application.properties

    def logAppender = new ListAppender<ILoggingEvent>()
    def web3j = Mock(Web3j)
    def scheduler = Executors.newScheduledThreadPool(1)

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
        registry.add("eagerStart", { "false" })
        registry.add("aws.dynamodb.table-prefix", { "" })
        registry.add("aws.s3.bucket-name", { "abijson-local-bucket" })
    }

    def setupSpec() {
        // Create DynamoDB client for LocalStack
        dynamoDbClient = DynamoDbClient.builder()
                .endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create("access123", "secret123")))
                .region(Region.AP_NORTHEAST_1)
                .build()

        // Create S3 client for LocalStack
        s3Client = S3Client.builder()
                .endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create("access123", "secret123")))
                .region(Region.AP_NORTHEAST_1)
                .forcePathStyle(true)
                .build()

        // Create tables and bucket
        AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
        AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
        AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
    }

    def cleanupSpec() {
        dynamoDbClient?.close()
        s3Client?.close()
    }

    def setup() {
        // Clear all S3 bucket contents completely
        clearS3Bucket()
        // Upload real ABI files to
        AdhocHelper.uploadRealAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
        // Clear all DynamoDB table contents
        clearDynamoDBTables()
        // Setup web3j mock
        setupWeb3jMock()
        // Start log appender to capture logs
        logAppender.start()
        logger.addAppender(logAppender)
        logger2.addAppender(logAppender)
    }

    private void setupWeb3jMock() {

        def field = Web3jConfig.class.getDeclaredField("web3j")
        field.setAccessible(true)
        field.set(web3jConfig, web3j)
        println("Web3j mock setup completed")
    }

    private void setUpEventStream(List<NewHeadsNotification> blocks) {
        def processor = PublishProcessor.<NewHeadsNotification> create()
        def index = new AtomicInteger(0)
        scheduler.scheduleAtFixedRate({
            int i = index.getAndIncrement()
            if (i < blocks.size()) {
                processor.onNext(blocks.get(i))
            }
        }, 0, 2, TimeUnit.SECONDS)

        web3j.newHeadsNotifications() >> Flowable.fromPublisher(processor)
    }

    private void setUpPendingEvent(List<EthLog.LogResult> resultList) {
        def mockRequest = Mock(Request)
        def mockLog = Mock(EthLog)

        web3j.ethGetLogs(_ as EthFilter) >> mockRequest
        mockRequest.send() >> mockLog
        mockLog.getLogs() >> resultList
    }

    def cleanup() {
        // Clear S3 bucket for next test
        clearS3Bucket()
        // Shut down the scheduler to stop mock event generation
        scheduler.shutdown()
        scheduler.awaitTermination(5, TimeUnit.SECONDS)
    }

    private void clearS3Bucket() {
        try {
            String continuationToken = null
            boolean hasMoreObjects = true

            while (hasMoreObjects) {
                def listRequest = ListObjectsV2Request.builder()
                        .bucket(TEST_BUCKET)
                        .continuationToken(continuationToken)
                        .build()

                def listResponse = s3Client.listObjectsV2(listRequest as ListObjectsV2Request)

                listResponse.contents().each { obj ->
                    s3Client.deleteObject(DeleteObjectRequest.builder()
                            .bucket(TEST_BUCKET)
                            .key(obj.key())
                            .build() as DeleteObjectRequest)
                }

                continuationToken = listResponse.nextContinuationToken()
                hasMoreObjects = (continuationToken != null)
            }

            def finalCheck = s3Client.listObjectsV2 {
                it.bucket(TEST_BUCKET)
            }
            if (finalCheck.contents().isEmpty()) {
                println("S3 bucket ${TEST_BUCKET} successfully cleared")
            } else {
                println("Warning: S3 bucket ${TEST_BUCKET} still contains ${finalCheck.contents().size()} objects")
            }
        } catch (Exception e) {
            println("Error clearing S3 bucket: ${e.message}")
            e.printStackTrace()
        }
    }

    private void clearDynamoDBTables() {
        try {
            // Clear events table
            clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

            // Clear block height table
            clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
        } catch (Exception e) {
            println("Error clearing DynamoDB tables: ${e.message}")
            e.printStackTrace()
        }
    }

    private void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
        try {
            // Scan the table to get all items
            def scanRequest = ScanRequest.builder()
                    .tableName(tableName)
                    .build()

            def scanResponse = dynamoDbClient.scan(scanRequest as ScanRequest)

            // Delete each item
            scanResponse.items().each { item ->
                def keyMap = [:]
                keyAttributes.each { keyAttr ->
                    if (item.containsKey(keyAttr)) {
                        keyMap[keyAttr] = item[keyAttr]
                    }
                }

                if (!keyMap.isEmpty()) {
                    def deleteRequest = DeleteItemRequest.builder()
                            .tableName(tableName)
                            .key(keyMap as Map<String, AttributeValue>)
                            .build()
                    dynamoDbClient.deleteItem(deleteRequest as DeleteItemRequest)
                }
            }
        } catch (Exception e) {
            println("Error clearing DynamoDB table ${tableName}: ${e.message}")
        }
    }

    /**
     * Successful Service Startup with command line runner and process ABI files
     * Verifies service starts successfully with all dependencies valid
     * Expected: Service logs "Started bc monitoring" and extracted from correct JSON path based on format
     */
    def "Should service start up successfully with command line runner and process ABI files"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        when: "Running service with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
    }

    /**
     * Should processes ABI files from multiple zones
     * Verifies service processes ABI files from multiple zones
     * Expected: Service logs "getting s3 abi object" for each zone
     */
    def "Should processes ABI files from multiple zones"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        AdhocHelper.uploadRealAbiFiles(s3Client, TEST_BUCKET, "3001", [
                "Token",
                "Account",
                "Provider"
        ])

        when: "Running service with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3000") }
        assert messages.any { it.contains("getting s3 abi object. bucketName=abijson-local-bucket, objKey=3001") }
        assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
    }

    /**
     * Should correctly parses ABI files based on truffle environment variable
     * Verifies service correctly parses ABI files based on truffle environment variable
     * Expected: No exceptions are thrown and service starts successfully
     */
    def "Should correctly parses ABI files based on truffle environment variable"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        clearS3Bucket()
        properties.setAbiFormat("truffle")
        AdhocHelper.uploadRealAbiFilesTest(s3Client, TEST_BUCKET, "3000", ["Account"])

        when: "Running service with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        and: "Service starts successfully"
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("ABI file processed: address=") && it.contains("contract_name=") && it.contains("last_modified=") && it.contains("events=") }
    }

    /**
     * Should skip non-json file
     * Verifies service skips non-json files
     * Expected: Service logs "This object will be skipped because the extension is not .json" and no exceptions are thrown
     */
    def "Should skip non-json file"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        clearS3Bucket()
        AdhocHelper.uploadRealAbiFilesNotJson(s3Client, TEST_BUCKET, "3000", ["nonJson.txt"])

        when: "Running service with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then: "No exceptions are thrown"
        noExceptionThrown()

        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("This object will be skipped because the extension is not .json:") }
    }

    /**
     * Should skip objects that are not direct children of the current prefix
     * Verifies service skip objects
     * Expected: Service logs "Failed to list S3 CommonPrefixes objects"
     */
    def "Should skip objects that are not direct children of the current prefix"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        clearS3Bucket()
        AdhocHelper.uploadRealAbiFilesWrongPrefix(s3Client, null, null, [
                "Account"
        ])

        when: "Running bcmonitoring with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then:
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Started bc monitoring") }
        assert messages.any { it.contains("Failed to list S3 CommonPrefixes objects") }
    }

    /**
     * Should start fails when ABI file contains malformed JSON
     * Verifies service startup fails
     * Expected: Service logs "Failed to parse S3 abi object:"
     */
    def "Should start fails when ABI file contains malformed JSON"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        clearS3Bucket()
        AdhocHelper.uploadRealAbiFilesTest(s3Client, TEST_BUCKET, "3000", [
                "ErrorJson"
        ])

        when: "Running bcmonitoring with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then:
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("Failed to parse S3 abi object:") }
    }

    /**
     * Should start fails when ABI file missing abi
     * Verifies service startup fails
     * Expected: Service logs "ABI section not found in JSON"
     */
    def "Should start fails when ABI file missing abi"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream([])
        setUpPendingEvent([])

        clearS3Bucket()
        AdhocHelper.uploadRealAbiFilesTest(s3Client, TEST_BUCKET, "3000", [
                "ErrorABI"
        ])

        when: "Running bcmonitoring with command line runner"
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule({
            AdhocHelper.stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)
        commandLineRunner.run("-f")

        then:
        def messages = logAppender.list*.formattedMessage
        assert messages.any { it.contains("ABI section not found in JSON") }
    }
}
